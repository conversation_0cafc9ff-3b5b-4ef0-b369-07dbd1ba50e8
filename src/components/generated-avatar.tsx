import { avataaarsNeutral, initials } from "@dicebear/collection";
import { createAvatar } from "@dicebear/core";
import type { FC } from "react";
import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";

interface IGeneratedAvatarProps extends React.ComponentProps<typeof Avatar> {
  seed: string;
  variant?: "avataaarsNeutral" | "initials";
}

export const GeneratedAvatar: FC<IGeneratedAvatarProps> = ({
  seed,
  variant,
  className,
  ...rest
}) => {
  let avatar: ReturnType<typeof createAvatar>;

  if (variant === "avataaarsNeutral") {
    avatar = createAvatar(avataaarsNeutral, {
      seed,
    });
  } else {
    avatar = createAvatar(initials, {
      seed,
      fontWeight: 500,
      fontSize: 42,
    });
  }

  return (
    <Avatar className={cn(className)} {...rest}>
      <AvatarImage src={avatar.toDataUri()} alt="Generated Avatar" />
      <AvatarFallback>{seed.slice(0, 2).toUpperCase()}</AvatarFallback>
    </Avatar>
  );
};
