"use client";

import { ChevronDownIcon, CreditCardIcon, LogOutIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import type { FC } from "react";
import { GeneratedAvatar } from "@/components/generated-avatar";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { authClient } from "@/lib/auth-client";

export const DashboardUserButton: FC = () => {
  const router = useRouter();
  const { data, isPending } = authClient.useSession();

  const onLogout = () => {
    authClient.signOut({
      fetchOptions: {
        onSuccess: () => {
          router.push("/sign-in");
        },
      },
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="rounded-lg border border-border/10 p-3 w-full flex items-center justify-between bg-foreground/5 hover:bg-foreground/10 overflow-hidden cursor-pointer">
        {isPending || !data?.user.name ? (
          <>
            <Skeleton className="size-8 rounded-full bg-foreground/10 mr-3" />
            <div className="flex flex-col text-left overflow-hidden gap-2 flex-1 min-w-0">
              <Skeleton className="h-4 w-10 bg-foreground/10" />
              <Skeleton className="h-3 w-30 bg-foreground/10" />
            </div>
          </>
        ) : (
          <>
            {data.user.image ? (
              <Avatar className="mr-3">
                <AvatarImage src={data.user.image} />
              </Avatar>
            ) : (
              <GeneratedAvatar
                seed={data.user.name || data.user.email}
                variant="avataaarsNeutral"
                className="mr-3"
              />
            )}
            <div className="flex flex-col text-left overflow-hidden flex-1 min-w-0">
              <p className="text-sm truncate w-full">{data.user.name}</p>
              <p className="text-xs truncate w-full">{data.user.email}</p>
            </div>
          </>
        )}
        <ChevronDownIcon className="size-4 shrink-0" />
      </DropdownMenuTrigger>
      {isPending || !data?.user.name ? null : (
        <DropdownMenuContent align="end" side="right" className="w-72">
          <DropdownMenuLabel>
            <div className="flex flex-col">
              <span className="font-medium truncate"> {data.user.name}</span>
              <span className="text-sm font-normal text-muted-foreground truncate">
                {data.user.email}
              </span>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem className="cursor-pointer flex items-center justify-between">
            Billing
            <CreditCardIcon className="size-4" />
          </DropdownMenuItem>
          <DropdownMenuItem
            className="cursor-pointer flex items-center justify-between"
            onClick={onLogout}
          >
            Logout
            <LogOutIcon className="size-4" />
          </DropdownMenuItem>
        </DropdownMenuContent>
      )}
    </DropdownMenu>
  );
};
