import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTRPC } from "@/trpc/client";

export function useAllPodcasts() {
  const trpc = useTRPC();
  return useQuery(trpc.podcasts.getAll.queryOptions());
}

export function usePodcast(id: string) {
  const trpc = useTRPC();
  return useQuery(trpc.podcasts.get.queryOptions({ id }));
}

export function useCreatePodcast() {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  return useMutation({
    ...trpc.podcasts.create.mutationOptions(),
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries(trpc.podcasts.getAll.queryKey());
      trpc.podcasts.create.mutationOptions().onSuccess?.(...args);
    },
  });
}

export function useUpdatePodcast() {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  return useMutation({
    ...trpc.podcasts.update.mutationOptions(),
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries(trpc.podcasts.getAll.queryKey());
      trpc.podcasts.update.mutationOptions().onSuccess?.(...args);
    },
  });
}

export function useDeletePodcast() {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  return useMutation({
    ...trpc.podcasts.delete.mutationOptions(),
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries(trpc.podcasts.getAll.queryKey());
      trpc.podcasts.delete.mutationOptions().onSuccess?.(...args);
    },
  });
}
