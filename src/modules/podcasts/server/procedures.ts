import { eq } from "drizzle-orm";
import z from "zod";
import { db } from "@/db";
import { podcast } from "@/db/schema";
import { createTRPCRouter, protectedOrgProcedure } from "@/trpc/init";

export const podcastsRouter = createTRPCRouter({
  create: protectedOrgProcedure
    .input(
      z.object({
        name: z.string(),
        subdomain: z.string(),
        description: z.string().optional(),
        image: z.string().optional(),
      }),
    )
    .mutation(async (opts) => {
      const { name, subdomain, description, image } = opts.input;
      const organizationId = opts.ctx.org.id;

      const [pod] = await db
        .insert(podcast)
        .values({
          name,
          subdomain,
          description,
          image,
          organizationId,
        })
        .returning();
      return pod;
    }),
  get: protectedOrgProcedure
    .input(z.object({ id: z.string() }))
    .query(async (opts) => {
      const { id } = opts.input;
      const podcast = await db.query.podcast.findFirst({
        where: (p, { eq }) => eq(p.id, id),
      });
      return podcast;
    }),
  getAll: protectedOrgProcedure.query(async () => {
    const podcasts = await db.query.podcast.findMany();
    return podcasts;
  }),
  update: protectedOrgProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().optional(),
        subdomain: z.string().optional(),
        description: z.string().optional(),
        image: z.string().optional(),
      }),
    )
    .mutation(async (opts) => {
      const { id, name, subdomain, description, image } = opts.input;
      const [pod] = await db
        .update(podcast)
        .set({
          name,
          subdomain,
          description,
          image,
        })
        .where(eq(podcast.id, id))
        .returning();
      return pod;
    }),
  delete: protectedOrgProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async (opts) => {
      const { id } = opts.input;
      await db.delete(podcast).where(eq(podcast.id, id));
      return true;
    }),
});
