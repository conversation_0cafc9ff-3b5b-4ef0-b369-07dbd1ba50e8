import type { z } from "zod";
import type { Site } from "@/generated/prisma";
import type { SiteCreateInput, SiteUpdateInput } from "./sites.schema";

export interface DTO {
  // editor/dashboard
  id: Site["id"];
  name: Site["name"];
  subdomain: Site["subdomain"];
  createdAt: Site["createdAt"];
  updatedAt: Site["updatedAt"];
}

export interface PublicDTO {
  // public: [subdomain].host.com
  subdomain: Site["subdomain"];
  name: Site["name"];
}

export interface ISitesQueries {
  count: () => Promise<number>;
  get: ({ id }: { id: string }) => Promise<DTO | null>;
  getAll: () => Promise<DTO[]>;
  getBySubdomain: ({
    subdomain,
  }: {
    subdomain: string;
  }) => Promise<PublicDTO | null>;
}

export interface ISitesMutations {
  create: ({
    data,
  }: {
    data: z.infer<typeof SiteCreateInput>;
  }) => Promise<DTO | null>;
  delete: ({ id }: { id: string }) => Promise<boolean>;
  update: ({
    id,
    data,
  }: {
    id: string;
    data: Partial<z.infer<typeof SiteUpdateInput>>;
  }) => Promise<DTO | null>;
}
